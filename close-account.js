"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var spl_token_1 = require("@solana/spl-token");
var web3_js_1 = require("@solana/web3.js");
var bignumber_js_1 = require("bignumber.js");
var bs58_1 = require("bs58");
var connection = new web3_js_1.Connection(
// 'https://occupational-bulbs-yjzlpcdxiv-dedicated.helius-rpc.com?api-key=c7e90625-f6fa-4825-8033-df63e4fdc37b',
"https://mainnet.helius-rpc.com/?api-key=3a15d997-1c89-4dc8-ac2b-6b33760835c6", "confirmed");
function buildPriorityFeeIxns(priorityFee, estimatedUnitsConsumed, type) {
    var computeUnits = type ? 500 : Math.round(estimatedUnitsConsumed * 1.1); // increase by 10%
    var modifyComputeUnits = web3_js_1.ComputeBudgetProgram.setComputeUnitLimit({
        units: computeUnits,
    });
    var priceInMicro = new bignumber_js_1.default(priorityFee)
        .multipliedBy(Math.pow(10, 15)) // micro = 10^-6 lamports, 1 lamport = 10^-9 sol => micro = 10^-15 sol
        .dividedBy(computeUnits)
        .toFixed(0);
    var setCuPrice = web3_js_1.ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: Number(priceInMicro),
    });
    return [modifyComputeUnits, setCuPrice];
}
var closeAccFunc = function (privateKey, excludedToken, priorityFee) { return __awaiter(void 0, void 0, void 0, function () {
    var signer, transaction, bata, filteredAccs, needAccs, instructions, signature;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0:
                console.log("\n\n");
                signer = web3_js_1.Keypair.fromSecretKey(bs58_1.default.decode(privateKey));
                console.log("public key for this account: ", signer.publicKey);
                transaction = new web3_js_1.Transaction();
                return [4 /*yield*/, connection.getParsedTokenAccountsByOwner(signer.publicKey, {
                        programId: new web3_js_1.PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), // token
                        // programId: new PublicKey("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"), // token 2022
                    })];
            case 1:
                bata = _a.sent();
                console.log("Number of token accounts: ", bata.value.length);
                filteredAccs = bata.value.filter(function (item) {
                    if (item.account.data.parsed.info.mint ==
                        "So11111111111111111111111111111111111111112" ||
                        excludedToken.includes(item.account.data.parsed.info.mint) ||
                        item.account.data.parsed.info.state == "frozen") {
                        return false;
                    }
                    return true;
                });
                console.log("Number of accounts are able to close: ", filteredAccs.length);
                needAccs = filteredAccs.slice(0, 10);
                console.log("Actual number of accounts closing: ", needAccs.length);
                if (needAccs.length == 0) {
                    console.log("✅ Không có tài khoản token nào để đóng. Ví đã sạch!");
                    return; // Thoát thay vì throw error
                }
                instructions = [];
                if (priorityFee > 0) {
                    instructions.push.apply(instructions, buildPriorityFeeIxns(priorityFee, 100000));
                }
                needAccs.map(function (item) {
                    var _a, _b, _c, _d;
                    if (Number(item.account.data.parsed.info.tokenAmount.amount) > 0 &&
                        item.account.data.parsed.info.mint !=
                            "So11111111111111111111111111111111111111112") {
                        var burnIxn = (0, spl_token_1.createBurnCheckedInstruction)(item.pubkey, new web3_js_1.PublicKey(item.account.data.parsed.info.mint), signer.publicKey, Number(item.account.data.parsed.info.tokenAmount.amount), item.account.data.parsed.info.tokenAmount.decimals, signer.publicKey, item.account.owner);
                        instructions.push(burnIxn);
                    }
                    // for token 2022
                    if (((_d = (_c = (_b = (_a = item.account.data.parsed.info) === null || _a === void 0 ? void 0 : _a.extensions) === null || _b === void 0 ? void 0 : _b[1]) === null || _c === void 0 ? void 0 : _c.state) === null || _d === void 0 ? void 0 : _d.withheldAmount) > 0) {
                        var withdrawFeeIxn = (0, spl_token_1.createHarvestWithheldTokensToMintInstruction)(new web3_js_1.PublicKey(item.account.data.parsed.info.mint), [item.pubkey], item.account.owner);
                        instructions.push(withdrawFeeIxn);
                    }
                    var closeIxn = (0, spl_token_1.createCloseAccountInstruction)(item.pubkey, signer.publicKey, signer.publicKey, [signer.publicKey], item.account.owner);
                    instructions.push(closeIxn);
                });
                if (instructions.length == 0) {
                    throw new Error("Have no instructions");
                }
                transaction.add.apply(transaction, instructions);
                return [4 /*yield*/, (0, web3_js_1.sendAndConfirmTransaction)(connection, transaction, [
                        signer,
                    ])];
            case 2:
                signature = _a.sent();
                console.log("signature: ", signature);
                return [2 /*return*/];
        }
    });
}); };
var run = function (privateKey, excludedToken, priorityFee) { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0:
                if (!true) return [3 /*break*/, 2];
                return [4 /*yield*/, closeAccFunc(privateKey, excludedToken, priorityFee)];
            case 1:
                _a.sent();
                // Nếu không có tài khoản để đóng, chờ 30 giây rồi thử lại
                console.log("⏳ Chờ 30 giây trước khi kiểm tra lại...");
                return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 30000); })];
            case 2:
                _a.sent();
                return [3 /*break*/, 0];
            case 3: return [2 /*return*/];
        }
    });
}); };
// điền private key vào đây
var privateKey = ''
// không đóng  các token này
var excludedToken = [
    // "7fRQixgMhULQxNQzaJrFEZyjmrBgJi85up9gZrcupump",
]; // địa chỉ token cách nhau bằng dấu phẩy
var priorityFee = 0.00001; // phí ưu tiên gửi lệnh đóng tài khoản
run(privateKey, excludedToken, priorityFee);
// script runs this tool: npx ts-node close-account.js
